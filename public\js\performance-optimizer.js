// 性能优化器 - 用于优化price.html页面的性能
class PerformanceOptimizer {
    constructor() {
        this.animationEnabled = true;
        this.reducedMotion = false;
        this.init();
    }

    init() {
        // 检测用户是否偏好减少动画
        this.checkReducedMotionPreference();
        
        // 添加性能控制面板
        this.addPerformanceControls();
        
        // 优化动画
        this.optimizeAnimations();
        
        // 监听页面可见性变化
        this.handleVisibilityChange();
        
        // 优化Chart.js性能
        this.optimizeCharts();
    }

    checkReducedMotionPreference() {
        if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            this.reducedMotion = true;
            this.disableAnimations();
        }
    }

    addPerformanceControls() {
        // 创建性能控制按钮
        const controlsContainer = document.createElement('div');
        controlsContainer.id = 'performance-controls';
        controlsContainer.style.cssText = `
            position: fixed;
            top: 10px;
            right: 80px;
            z-index: 10000;
            display: flex;
            gap: 5px;
        `;

        // 动画开关按钮
        const animationToggle = document.createElement('button');
        animationToggle.innerHTML = '<i class="fas fa-magic"></i>';
        animationToggle.title = '切换动画效果';
        animationToggle.className = 'clay-button';
        animationToggle.style.cssText = `
            width: 40px;
            height: 40px;
            font-size: 14px;
            background: ${this.animationEnabled ? 'var(--clay-primary)' : 'var(--clay-secondary-text)'};
            color: white;
        `;
        
        animationToggle.onclick = () => this.toggleAnimations();

        controlsContainer.appendChild(animationToggle);
        document.body.appendChild(controlsContainer);
        
        this.animationToggle = animationToggle;
    }

    toggleAnimations() {
        this.animationEnabled = !this.animationEnabled;
        
        if (this.animationEnabled) {
            this.enableAnimations();
            this.animationToggle.style.background = 'var(--clay-primary)';
            this.animationToggle.title = '关闭动画效果';
            this.showNotification('动画效果已开启', 'success');
        } else {
            this.disableAnimations();
            this.animationToggle.style.background = 'var(--clay-secondary-text)';
            this.animationToggle.title = '开启动画效果';
            this.showNotification('动画效果已关闭，性能已优化', 'info');
        }
    }

    disableAnimations() {
        // 添加CSS规则禁用动画
        const style = document.createElement('style');
        style.id = 'performance-optimizer-style';
        style.textContent = `
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
                scroll-behavior: auto !important;
            }
            
            /* 保留必要的过渡效果 */
            .modal, .toast {
                transition-duration: 0.2s !important;
            }
        `;
        document.head.appendChild(style);
    }

    enableAnimations() {
        const style = document.getElementById('performance-optimizer-style');
        if (style) {
            style.remove();
        }
    }

    optimizeAnimations() {
        // 使用Intersection Observer优化动画
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in-view');
                    } else {
                        entry.target.classList.remove('animate-in-view');
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });

            // 观察需要动画的元素
            document.querySelectorAll('.quick-button, .form-section, .history-table tr').forEach(el => {
                observer.observe(el);
            });
        }
    }

    handleVisibilityChange() {
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // 页面不可见时暂停动画
                this.pauseAnimations();
            } else {
                // 页面可见时恢复动画
                this.resumeAnimations();
            }
        });
    }

    pauseAnimations() {
        document.body.style.animationPlayState = 'paused';
        document.querySelectorAll('*').forEach(el => {
            el.style.animationPlayState = 'paused';
        });
    }

    resumeAnimations() {
        if (this.animationEnabled) {
            document.body.style.animationPlayState = 'running';
            document.querySelectorAll('*').forEach(el => {
                el.style.animationPlayState = 'running';
            });
        }
    }

    optimizeCharts() {
        // 延迟加载Chart.js相关功能
        if (typeof Chart !== 'undefined') {
            Chart.defaults.animation = {
                duration: this.animationEnabled ? 1000 : 0
            };
            
            Chart.defaults.responsive = true;
            Chart.defaults.maintainAspectRatio = false;
            
            // 减少图表更新频率
            Chart.defaults.elements.point.radius = 3;
            Chart.defaults.elements.line.tension = 0.1;
        }
    }

    showNotification(message, type = 'info') {
        // 使用现有的showNotification函数，如果存在的话
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else {
            console.log(`[性能优化器] ${message}`);
        }
    }

    // 监控性能指标
    monitorPerformance() {
        if ('performance' in window) {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    if (entry.entryType === 'measure') {
                        console.log(`性能指标: ${entry.name} - ${entry.duration.toFixed(2)}ms`);
                    }
                });
            });
            
            observer.observe({ entryTypes: ['measure', 'navigation'] });
        }
    }

    // 内存清理
    cleanup() {
        // 清理不必要的事件监听器和定时器
        const timers = [];
        const originalSetTimeout = window.setTimeout;
        const originalSetInterval = window.setInterval;
        
        window.setTimeout = function(fn, delay) {
            const id = originalSetTimeout(fn, delay);
            timers.push(id);
            return id;
        };
        
        window.setInterval = function(fn, delay) {
            const id = originalSetInterval(fn, delay);
            timers.push(id);
            return id;
        };
        
        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            timers.forEach(id => {
                clearTimeout(id);
                clearInterval(id);
            });
        });
    }
}

// 页面加载完成后初始化性能优化器
document.addEventListener('DOMContentLoaded', () => {
    // 延迟初始化以避免阻塞页面加载
    setTimeout(() => {
        window.performanceOptimizer = new PerformanceOptimizer();
        console.log('[性能优化器] 已启动');
    }, 1000);
});

// 导出供其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceOptimizer;
}
